import { describe, it, expect, vi, beforeEach } from 'vitest';
import { processThinkingResponse } from '../src/lib/utils/thinkingUtils';

// Test the empty response handling logic directly
// Since the full API service has complex mocking requirements,
// we'll test the core logic that handles empty responses

describe('API Service', () => {
    const mockGet = vi.mocked(get);
    const mockShowStatus = vi.mocked(showStatus);

    beforeEach(() => {
        vi.clearAllMocks();
        
        // Setup default mock returns
        mockGet.mockImplementation((store) => {
            if (store === apiKeys) {
                return {
                    gemini: 'test-gemini-key',
                    openai: 'test-openai-key',
                    customUrl: 'https://api.custom.com',
                    customKey: 'test-custom-key'
                };
            }
            if (store === appSettings) {
                return {
                    aiProvider: 'gemini',
                    aiModel: 'gemini-1.5-pro'
                };
            }
            return {};
        });
    });

    describe('callAIWithThinking', () => {
        it('should handle empty AI response gracefully', async () => {
            // Mock the adapter to return empty string
            const { GeminiAdapter } = await import('../src/lib/ai/geminiAdapter');
            const mockAdapter = new GeminiAdapter('test-key', 'gemini-1.5-pro');
            vi.mocked(mockAdapter.generate).mockResolvedValue('');

            const result = await callAIWithThinking('test prompt');

            expect(result).toEqual({
                content: '',
                thinking: null,
                hasThinking: false
            });

            expect(mockShowStatus).toHaveBeenCalledWith(
                'AI responded with empty content. Try rephrasing your prompt.',
                'info',
                5000
            );
        });

        it('should handle whitespace-only AI response gracefully', async () => {
            // Mock the adapter to return whitespace
            const { GeminiAdapter } = await import('../src/lib/ai/geminiAdapter');
            const mockAdapter = new GeminiAdapter('test-key', 'gemini-1.5-pro');
            vi.mocked(mockAdapter.generate).mockResolvedValue('   \n\t   ');

            const result = await callAIWithThinking('test prompt');

            expect(result).toEqual({
                content: '',
                thinking: null,
                hasThinking: false
            });

            expect(mockShowStatus).toHaveBeenCalledWith(
                'AI responded with empty content. Try rephrasing your prompt.',
                'info',
                5000
            );
        });

        it('should process normal AI response correctly', async () => {
            // Mock the adapter to return normal content
            const { GeminiAdapter } = await import('../src/lib/ai/geminiAdapter');
            const mockAdapter = new GeminiAdapter('test-key', 'gemini-1.5-pro');
            vi.mocked(mockAdapter.generate).mockResolvedValue('This is a normal response');

            const result = await callAIWithThinking('test prompt');

            expect(result).toEqual({
                content: 'This is a normal response',
                thinking: null,
                hasThinking: false
            });

            expect(mockShowStatus).toHaveBeenCalledWith('AI response received.', 'success');
        });

        it('should handle AI adapter errors', async () => {
            // Mock the adapter to throw an error
            const { GeminiAdapter } = await import('../src/lib/ai/geminiAdapter');
            const mockAdapter = new GeminiAdapter('test-key', 'gemini-1.5-pro');
            vi.mocked(mockAdapter.generate).mockRejectedValue(new Error('API Error'));

            const result = await callAIWithThinking('test prompt');

            expect(result).toBeNull();
            expect(mockShowStatus).toHaveBeenCalledWith('AI Error: API Error', 'error');
        });
    });
});
