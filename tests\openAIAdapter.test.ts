import { describe, it, expect, vi, beforeEach } from 'vitest';
import { OpenAIAdapter } from '../src/lib/ai/openAIAdapter';

// Mock the fetchWithTimeout function
vi.mock('../src/lib/ai/aiUtils', () => ({
    fetchWithTimeout: vi.fn()
}));

describe('OpenAIAdapter', () => {
    let adapter: OpenAIAdapter;
    const mockApiKey = 'test-api-key';
    
    beforeEach(() => {
        vi.clearAllMocks();
        adapter = new OpenAIAdapter(mockApiKey, 'gpt-4o');
    });

    describe('Response Processing', () => {
        it('should handle normal response content', async () => {
            const mockResponse = {
                choices: [{
                    message: {
                        content: 'This is a normal response'
                    }
                }]
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            const result = await adapter.generate('test prompt');
            expect(result).toBe('This is a normal response');
        });

        it('should handle empty response content gracefully', async () => {
            const mockResponse = {
                choices: [{
                    message: {
                        content: ''
                    }
                }]
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            const result = await adapter.generate('test prompt');
            expect(result).toBe('');
        });

        it('should handle unexpected response structure', async () => {
            const mockResponse = {};

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            await expect(adapter.generate('test prompt')).rejects.toThrow(
                'Unexpected OpenAI API response structure.'
            );
        });

        it('should handle missing message content', async () => {
            const mockResponse = {
                choices: [{
                    message: {}
                }]
            };

            vi.spyOn(adapter as any, 'makeRequest').mockResolvedValue(mockResponse);

            await expect(adapter.generate('test prompt')).rejects.toThrow(
                'Unexpected OpenAI API response structure.'
            );
        });
    });

    describe('URL Building', () => {
        it('should build correct URL for OpenAI API', () => {
            const adapter = new OpenAIAdapter(mockApiKey, 'gpt-4o');
            const url = (adapter as any).buildUrl();
            expect(url).toBe('https://api.openai.com/v1/chat/completions');
        });

        it('should build correct URL for custom endpoints', () => {
            const customUrl = 'https://api.custom.com/v1';
            const adapter = new OpenAIAdapter(mockApiKey, 'gpt-4o', customUrl);
            const url = (adapter as any).buildUrl();
            expect(url).toBe('https://api.custom.com/v1/chat/completions');
        });

        it('should handle custom URLs that already include the endpoint', () => {
            const customUrl = 'https://api.custom.com/v1/chat/completions';
            const adapter = new OpenAIAdapter(mockApiKey, 'gpt-4o', customUrl);
            const url = (adapter as any).buildUrl();
            expect(url).toBe('https://api.custom.com/v1/chat/completions');
        });
    });
});
